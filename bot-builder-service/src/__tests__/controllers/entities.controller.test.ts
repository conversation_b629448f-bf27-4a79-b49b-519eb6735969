import { Request, Response } from "express";
import { EntitiesController } from "../../controllers/entities.controller";
import { Models } from "@neuratalk/bot-store";
import { logger, UuidParams } from "@neuratalk/common";
import { AppContext } from "../../types/context.types";

// Mock dependencies
jest.mock("@neuratalk/common", () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

describe("EntitiesController", () => {
  let entitiesController: EntitiesController;
  let mockModels: jest.Mocked<Models>;
  let mockContext: jest.Mocked<AppContext>;
  let mockRequest: Partial<Request<UuidParams>>;
  let mockResponse: Partial<Response>;

  beforeEach(() => {
    // Mock Models
    mockModels = {
      Entities: {
        destroy: jest.fn(),
        findByPk: jest.fn(),
      },
      IntentUtterance: {
        findAll: jest.fn(),
      },
    } as any;

    // Mock AppContext
    mockContext = {
      db: {
        models: mockModels,
      },
    } as any;

    // Create controller instance
    entitiesController = new EntitiesController(mockContext);

    // Setup mock request and response
    mockRequest = {
      params: { id: "entity-123" },
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
    };

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe("delete", () => {
    it("should successfully delete an entity when not in use", async () => {
      // Mock that entity exists
      (mockModels.Entities.findByPk as jest.Mock).mockResolvedValue({ id: "entity-123" });
      // Mock that entity is not in use
      (mockModels.IntentUtterance.findAll as jest.Mock).mockResolvedValue([]);
      // Mock successful deletion
      (mockModels.Entities.destroy as jest.Mock).mockResolvedValue(1);

      await entitiesController.delete(mockRequest as Request<UuidParams>, mockResponse as Response);

      expect(mockModels.Entities.findByPk).toHaveBeenCalledWith("entity-123");
      expect(mockModels.IntentUtterance.findAll).toHaveBeenCalledWith({
        where: { entityIds: { [expect.any(Symbol)]: "%entity-123%" } },
      });
      expect(mockModels.Entities.destroy).toHaveBeenCalledWith({ where: { id: "entity-123" } });
      expect(logger.info).toHaveBeenCalledWith("Entity deleted: entity-123");
      expect(mockResponse.status).toHaveBeenCalledWith(204);
      expect(mockResponse.send).toHaveBeenCalled();
    });

    it("should return 400 when entity is in use", async () => {
      // Mock that entity exists
      (mockModels.Entities.findByPk as jest.Mock).mockResolvedValue({ id: "entity-123" });
      // Mock that entity is in use
      (mockModels.IntentUtterance.findAll as jest.Mock).mockResolvedValue([
        { id: "utterance-1", text: "test utterance" },
      ]);

      await entitiesController.delete(mockRequest as Request<UuidParams>, mockResponse as Response);

      expect(mockModels.Entities.findByPk).toHaveBeenCalledWith("entity-123");
      expect(mockModels.IntentUtterance.findAll).toHaveBeenCalledWith({
        where: { entityIds: { [expect.any(Symbol)]: "%entity-123%" } },
      });
      expect(mockModels.Entities.destroy).not.toHaveBeenCalled();
      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: {
            code: "ENTITY_IN_USE",
            message: "Entity is being used in utterances and cannot be deleted",
          },
          timestamp: expect.any(Date),
        }),
      );
    });

    it("should return 404 when entity is not found initially", async () => {
      // Mock that entity doesn't exist
      (mockModels.Entities.findByPk as jest.Mock).mockResolvedValue(null);

      await entitiesController.delete(mockRequest as Request<UuidParams>, mockResponse as Response);

      expect(mockModels.Entities.findByPk).toHaveBeenCalledWith("entity-123");
      expect(mockModels.IntentUtterance.findAll).not.toHaveBeenCalled();
      expect(mockModels.Entities.destroy).not.toHaveBeenCalled();
      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: {
            code: "NOT_FOUND",
            message: "Entity not found",
          },
          timestamp: expect.any(Date),
        }),
      );
    });

    it("should return 404 when entity destroy returns 0", async () => {
      // Mock that entity exists
      (mockModels.Entities.findByPk as jest.Mock).mockResolvedValue({ id: "entity-123" });
      // Mock that entity is not in use
      (mockModels.IntentUtterance.findAll as jest.Mock).mockResolvedValue([]);
      // Mock that entity doesn't exist during destroy (returns 0)
      (mockModels.Entities.destroy as jest.Mock).mockResolvedValue(0);

      await entitiesController.delete(mockRequest as Request<UuidParams>, mockResponse as Response);

      expect(mockModels.Entities.findByPk).toHaveBeenCalledWith("entity-123");
      expect(mockModels.IntentUtterance.findAll).toHaveBeenCalled();
      expect(mockModels.Entities.destroy).toHaveBeenCalledWith({ where: { id: "entity-123" } });
      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: {
            code: "NOT_FOUND",
            message: "Entity not found",
          },
          timestamp: expect.any(Date),
        }),
      );
    });

    it("should handle database errors", async () => {
      // Mock database error during findByPk
      const mockError = new Error("Database connection failed");
      (mockModels.Entities.findByPk as jest.Mock).mockRejectedValue(mockError);

      await entitiesController.delete(mockRequest as Request<UuidParams>, mockResponse as Response);

      expect(logger.error).toHaveBeenCalledWith("Error deleting entity:", mockError);
      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: {
            code: "INTERNAL_ERROR",
            message: "Internal server error",
          },
          timestamp: expect.any(Date),
        }),
      );
    });

    it("should handle missing entity ID parameter", async () => {
      mockRequest.params = { id: "" } as UuidParams;

      // Mock that entity doesn't exist (since id is empty)
      (mockModels.Entities.findByPk as jest.Mock).mockResolvedValue(null);

      await entitiesController.delete(mockRequest as Request<UuidParams>, mockResponse as Response);

      expect(mockModels.Entities.findByPk).toHaveBeenCalledWith("");
      expect(mockResponse.status).toHaveBeenCalledWith(404);
    });
  });
});
